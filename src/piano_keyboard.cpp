#if defined(_WIN32)
#define NOMINMAX
#endif
#include "piano_keyboard.h"
#include "audio_engine.h"
#include <algorithm>
#include <cmath>

PianoKeyboard::PianoKeyboard()
    : keyboard_position_(50.0f, 100.0f)
    , keyboard_size_(1200.0f, 200.0f)
    , white_key_size_(20.0f, 120.0f)
    , black_key_size_(12.0f, 80.0f)
    , auto_layout_enabled_(true)
    , keyboard_margin_(50.0f)
    , current_window_width_(1280)
    , current_window_height_(720)
    , white_key_color_(Color::FromRGB(255, 255, 255))
    , white_key_pressed_color_(Color::FromRGB(200, 200, 255))
    , black_key_color_(Color::FromRGB(30, 30, 30))
    , black_key_pressed_color_(Color::FromRGB(100, 100, 150))
    , key_border_color_(Color::FromRGB(100, 100, 100))
    , audio_engine_(nullptr)
    , audio_enabled_(true)
    , last_hovered_key_(-1)
{
}

PianoKeyboard::~PianoKeyboard() {
}

void PianoKeyboard::Initialize() {
    Initialize(nullptr);
}

void PianoKeyboard::Initialize(AudioEngine* audio_engine) {
    audio_engine_ = audio_engine;

    keys_.clear();
    keys_.reserve(128);

    for (int note = 0; note < 128; ++note) {
        PianoKey key;
        key.note = note;
        key.is_black = IsBlackKey(note);
        key.is_pressed = false;
        key.color = key.is_black ? black_key_color_ : white_key_color_;
        keys_.push_back(key);
    }

    CalculateKeyPositions();
}

void PianoKeyboard::Update() {
    // Update key colors based on pressed state
    for (auto& key : keys_) {
        if (key.is_pressed) {
            key.color = key.is_black ? black_key_pressed_color_ : white_key_pressed_color_;
        } else {
            key.color = key.is_black ? black_key_color_ : white_key_color_;
        }
    }
}

void PianoKeyboard::Render(OpenGLRenderer& renderer) {
    // Render white keys first (background)
    RenderWhiteKeys(renderer);

    // Render black keys on top
    RenderBlackKeys(renderer);
}

void PianoKeyboard::HandleInput(double mouse_x, double mouse_y, bool mouse_is_down) {
    Vec2 mouse_pos(mouse_x, mouse_y);
    int current_key = GetKeyAtPosition(mouse_pos);

    if (mouse_is_down) {
        if (current_key != last_hovered_key_) {
            // Stop the last key if we slid off it
            if (last_hovered_key_ != -1) {
                SetKeyPressed(last_hovered_key_, false);
            }
            // Start the new key
            if (current_key != -1) {
                SetKeyPressed(current_key, true);
            }
            last_hovered_key_ = current_key;
        }
    } else {
        // Mouse is up, release any pressed key
        if (last_hovered_key_ != -1) {
            SetKeyPressed(last_hovered_key_, false);
            last_hovered_key_ = -1;
        }
    }
}

bool PianoKeyboard::IsKeyPressed(int note) const {
    if (note >= 0 && note < 128) {
        return keys_[note].is_pressed;
    }
    return false;
}

void PianoKeyboard::SetKeyPressed(int note, bool pressed) {
    if (note >= 0 && note < 128) {
        bool was_pressed = keys_[note].is_pressed;
        keys_[note].is_pressed = pressed;

        // Handle audio playback
        if (audio_engine_ && audio_enabled_) {
            if (pressed && !was_pressed) {
                // Key was just pressed - play note
                audio_engine_->PlayNote(note, 127);
            } else if (!pressed && was_pressed) {
                // Key was just released - stop note
                audio_engine_->StopNote(note);
            }
        }
    }
}

void PianoKeyboard::SetKeyboardPosition(const Vec2& position) {
    keyboard_position_ = position;
    CalculateKeyPositions();
}

void PianoKeyboard::SetKeyboardSize(const Vec2& size) {
    keyboard_size_ = size;
    CalculateKeyPositions();
}

void PianoKeyboard::SetWhiteKeySize(const Vec2& size) {
    white_key_size_ = size;
    CalculateKeyPositions();
}

void PianoKeyboard::SetBlackKeySize(const Vec2& size) {
    black_key_size_ = size;
    CalculateKeyPositions();
}

void PianoKeyboard::UpdateLayout(int window_width, int window_height) {
    current_window_width_ = window_width;
    current_window_height_ = window_height;

    if (auto_layout_enabled_) {
        CalculateAutoLayout(window_width, window_height);
    }
}

void PianoKeyboard::SetAutoLayout(bool enabled) {
    auto_layout_enabled_ = enabled;
    if (enabled) {
        CalculateAutoLayout(current_window_width_, current_window_height_);
    }
}

void PianoKeyboard::SetKeyboardMargin(float margin) {
    keyboard_margin_ = margin;
    if (auto_layout_enabled_) {
        CalculateAutoLayout(current_window_width_, current_window_height_);
    }
}

int PianoKeyboard::GetPressedKeyCount() const {
    return std::count_if(keys_.begin(), keys_.end(), 
                        [](const PianoKey& key) { return key.is_pressed; });
}

std::vector<int> PianoKeyboard::GetPressedKeys() const {
    std::vector<int> pressed_keys;
    for (const auto& key : keys_) {
        if (key.is_pressed) {
            pressed_keys.push_back(key.note);
        }
    }
    return pressed_keys;
}

bool PianoKeyboard::IsBlackKey(int note) const {
    int octave_note = note % 12;
    return (octave_note == 1 || octave_note == 3 || octave_note == 6 || 
            octave_note == 8 || octave_note == 10);
}

void PianoKeyboard::CalculateKeyPositions() {
    // Calculate white key positions first
    float white_key_x = keyboard_position_.x;
    int white_key_count = 0;

    for (auto& key : keys_) {
        if (!key.is_black) {
            key.position = Vec2(white_key_x, keyboard_position_.y);
            key.size = white_key_size_;
            white_key_x += white_key_size_.x;
            white_key_count++;
        }
    }

    // Calculate black key positions
    for (auto& key : keys_) {
        if (key.is_black) {
            int white_key_index = GetWhiteKeyIndex(key.note);
            if (white_key_index >= 0) {
                float black_key_x = keyboard_position_.x + (white_key_index * white_key_size_.x) - (black_key_size_.x * 0.5f);
                key.position = Vec2(black_key_x, keyboard_position_.y);
                key.size = black_key_size_;
            }
        }
    }
}

int PianoKeyboard::GetWhiteKeyIndex(int note) const {
    int white_key_index = 0;
    for (int i = 0; i < note; ++i) {
        if (!IsBlackKey(i)) {
            white_key_index++;
        }
    }
    return white_key_index;
}

void PianoKeyboard::RenderWhiteKeys(OpenGLRenderer& renderer) {
    for (const auto& key : keys_) {
        if (!key.is_black) {
            renderer.DrawRectWithBorder(key.position, key.size, key.color, key_border_color_);
        }
    }
}

void PianoKeyboard::RenderBlackKeys(OpenGLRenderer& renderer) {
    for (const auto& key : keys_) {
        if (key.is_black) {
            renderer.DrawRectWithBorder(key.position, key.size, key.color, key_border_color_);
        }
    }
}

int PianoKeyboard::GetKeyAtPosition(const Vec2& pos) const {
    // Check black keys first (they're on top)
    for (const auto& key : keys_) {
        if (key.is_black) {
            if (pos.x >= key.position.x && pos.x <= key.position.x + key.size.x &&
                pos.y >= key.position.y && pos.y <= key.position.y + key.size.y) {
                return key.note;
            }
        }
    }

    // Then check white keys
    for (const auto& key : keys_) {
        if (!key.is_black) {
            if (pos.x >= key.position.x && pos.x <= key.position.x + key.size.x &&
                pos.y >= key.position.y && pos.y <= key.position.y + key.size.y) {
                return key.note;
            }
        }
    }

    return -1; // No key found
}

void PianoKeyboard::CalculateAutoLayout(int window_width, int window_height) {
    // Calculate total number of white keys
    int total_white_keys = GetTotalWhiteKeys();

    // Calculate available width for keyboard (minus margins)
    float available_width = window_width - (keyboard_margin_ * 2.0f);

    // Calculate white key width based on available space
    float white_key_width = available_width / total_white_keys;

    // Limit minimum and maximum key sizes for usability
    white_key_width = std::max(10.0f, std::min(white_key_width, 50.0f));

    // Calculate keyboard height (proportional to window height)
    float keyboard_height = std::min(window_height * 0.3f, 200.0f);
    keyboard_height = std::max(keyboard_height, 80.0f);

    // Update key sizes
    white_key_size_ = Vec2(white_key_width, keyboard_height);
    black_key_size_ = Vec2(white_key_width * 0.6f, keyboard_height * 0.65f);

    // Calculate total keyboard width
    float total_keyboard_width = total_white_keys * white_key_width;

    // Center the keyboard horizontally
    float keyboard_x = (window_width - total_keyboard_width) * 0.5f;

    // Position keyboard in the lower half of the window
    float keyboard_y = window_height * 0.6f;

    keyboard_position_ = Vec2(keyboard_x, keyboard_y);
    keyboard_size_ = Vec2(total_keyboard_width, keyboard_height);

    // Recalculate key positions
    CalculateKeyPositions();
}

int PianoKeyboard::GetTotalWhiteKeys() const {
    int count = 0;
    for (int i = 0; i < 128; ++i) {
        if (!IsBlackKey(i)) {
            count++;
        }
    }
    return count;
}

void PianoKeyboard::SetAudioEngine(AudioEngine* audio_engine) {
    audio_engine_ = audio_engine;
}

void PianoKeyboard::SetAudioEnabled(bool enabled) {
    audio_enabled_ = enabled;

    // If disabling audio, stop all currently playing notes
    if (!enabled && audio_engine_) {
        audio_engine_->StopAllNotes();
    }
}

bool PianoKeyboard::IsAudioEnabled() const {
    return audio_enabled_;
}
