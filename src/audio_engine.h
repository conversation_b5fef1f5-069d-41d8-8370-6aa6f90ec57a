#pragma once

#include <string>
#include <memory>

#if defined(_WIN32)
#include <windows.h>
#else
#include <dlfcn.h>
#endif

#define NOBASSMIDIOVERLOADS
#include "bass.h"
#include "bassmidi.h"
#include "bass_fx.h"

class AudioEngine {
public:
    AudioEngine();
    ~AudioEngine();

    // Initialize the audio engine
    bool Initialize();
    
    // Cleanup the audio engine
    void Cleanup();
    
    // Load a soundfont file (.sf2)
    bool LoadSoundfont(const std::string& soundfont_path);
    
    // Play a MIDI note
    void PlayNote(int note, int velocity = 127);
    
    // Stop a MIDI note
    void StopNote(int note);
    
    // Stop all notes
    void StopAllNotes();
    
    // Set master volume (0.0 to 1.0)
    void SetVolume(float volume);
    
    // Get current volume
    float GetVolume() const;
    
    // Check if audio engine is initialized
    bool IsInitialized() const;

    // Check if soundfont is loaded
    bool IsSoundfontLoaded() const;

    // Get current soundfont path
    const std::string& GetCurrentSoundfontPath() const;

    // Get polyphony information
    int GetCurrentPolyphony() const;
    int GetMaxPolyphony() const;

    // Test functions
    void PlayTestTone();
    void PrintDeviceInfo();

private:
    bool initialized_;
    HSTREAM midi_stream_;
    HSOUNDFONT soundfont_;
    float master_volume_;
    std::string current_soundfont_path_;

    // Polyphony tracking
    int max_polyphony_;
    mutable int current_polyphony_;

    // Dynamic library handles
    void* bass_lib_;
    void* bassmidi_lib_;
    void* bass_fx_lib_;

    // Function pointers using official BASS function signatures
    decltype(&BASS_Init) BASS_Init_ptr;
    decltype(&BASS_Free) BASS_Free_ptr;
    decltype(&BASS_MIDI_StreamCreate) BASS_MIDI_StreamCreate_ptr;
    decltype(&BASS_ChannelPlay) BASS_ChannelPlay_ptr;
    decltype(&BASS_ChannelSetAttribute) BASS_ChannelSetAttribute_ptr;
    decltype(&BASS_StreamFree) BASS_StreamFree_ptr;
    decltype(&BASS_MIDI_StreamEvent) BASS_MIDI_StreamEvent_ptr;
    decltype(&BASS_MIDI_FontInit) BASS_MIDI_FontInit_ptr;
    decltype(&BASS_MIDI_FontFree) BASS_MIDI_FontFree_ptr;
    decltype(&BASS_MIDI_StreamSetFonts) BASS_MIDI_StreamSetFonts_ptr;
    decltype(&BASS_ErrorGetCode) BASS_ErrorGetCode_ptr;
    decltype(&BASS_ChannelGetAttribute) BASS_ChannelGetAttribute_ptr;
    decltype(&BASS_ChannelIsActive) BASS_ChannelIsActive_ptr;
    decltype(&BASS_GetDeviceInfo) BASS_GetDeviceInfo_ptr;
    decltype(&BASS_SetConfig) BASS_SetConfig_ptr;

    // Helper functions
    bool LoadBASSLibraries();
    void UnloadBASSLibraries();
    bool InitializeBASS();
    bool InitializeMIDI();
    void CleanupBASS();
    void CleanupMIDI();

    // Error handling
    std::string GetLastBASSError() const;
    void LogBASSError(const std::string& operation) const;
};
