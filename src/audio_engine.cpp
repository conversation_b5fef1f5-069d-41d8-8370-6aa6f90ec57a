#if defined(_WIN32)
#define NOMINMAX
#endif
#include "audio_engine.h"
#include <iostream>
#include <vector>
#include <cstring>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#include <dlfcn.h>
#endif

// Note: All BASS constants and structures are now defined in the official headers
// MAKEWORD is also defined in bass.h

// Helper function to join path components
std::string JoinPath(const std::string& p1, const std::string& p2) {
#ifdef _WIN32
    if (p1.empty()) return p2;
    if (p2.empty()) return p1;
    char last_char = p1.back();
    if (last_char == '\\' || last_char == '/') {
        return p1 + p2;
    }
    return p1 + "\\" + p2;
#else
    if (p1.empty()) return p2;
    if (p2.empty()) return p1;
    char last_char = p1.back();
    if (last_char == '/') {
        return p1 + p2;
    }
    return p1 + "/" + p2;
#endif
}

// Helper function to get executable directory
#ifdef _WIN32
std::string GetExecutableDirectory() {
    char path[MAX_PATH];
    if (GetModuleFileNameA(NULL, path, MAX_PATH) == 0) {
        return ".";
    }
    std::string exe_path(path);
    size_t last_slash = exe_path.find_last_of('\\');
    if (last_slash != std::string::npos) {
        return exe_path.substr(0, last_slash);
    }
    return "."; // fallback to current directory
}
#else
std::string GetExecutableDirectory() {
    char path[1024];
    ssize_t len = readlink("/proc/self/exe", path, sizeof(path) - 1);
    if (len != -1) {
        path[len] = '\0';
        std::string exe_path(path);
        size_t last_slash = exe_path.find_last_of('/');
        if (last_slash != std::string::npos) {
            return exe_path.substr(0, last_slash);
        }
    }
    return "."; // fallback to current directory
}
#endif

AudioEngine::AudioEngine()
    : initialized_(false)
    , midi_stream_(0)
    , soundfont_(0)
    , master_volume_(0.8f)
    , current_soundfont_path_("")
    , max_polyphony_(256)  // Default BASS MIDI polyphony
    , current_polyphony_(0)
    , bass_lib_(nullptr)
    , bassmidi_lib_(nullptr)
    , bass_fx_lib_(nullptr)
    , BASS_Init_ptr(nullptr)
    , BASS_Free_ptr(nullptr)
    , BASS_MIDI_StreamCreate_ptr(nullptr)
    , BASS_ChannelPlay_ptr(nullptr)
    , BASS_ChannelSetAttribute_ptr(nullptr)
    , BASS_StreamFree_ptr(nullptr)
    , BASS_MIDI_StreamEvent_ptr(nullptr)
    , BASS_MIDI_FontInit_ptr(nullptr)
    , BASS_MIDI_FontFree_ptr(nullptr)
    , BASS_MIDI_StreamSetFonts_ptr(nullptr)
    , BASS_ErrorGetCode_ptr(nullptr)
    , BASS_ChannelGetAttribute_ptr(nullptr)
    , BASS_ChannelIsActive_ptr(nullptr)
    , BASS_GetDeviceInfo_ptr(nullptr)
{
}

AudioEngine::~AudioEngine() {
    Cleanup();
    UnloadBASSLibraries();
}

bool AudioEngine::Initialize() {
    if (initialized_) {
        return true;
    }

    std::cout << "Initializing Audio Engine..." << std::endl;

    if (!LoadBASSLibraries()) {
        std::cerr << "Failed to load BASS libraries" << std::endl;
        return false;
    }

    if (!InitializeBASS()) {
        std::cerr << "Failed to initialize BASS" << std::endl;
        UnloadBASSLibraries();
        return false;
    }

    if (!InitializeMIDI()) {
        std::cerr << "Failed to initialize BASSMIDI" << std::endl;
        CleanupBASS();
        UnloadBASSLibraries();
        return false;
    }

    initialized_ = true;
    std::cout << "Audio Engine initialized successfully" << std::endl;
    return true;
}

void AudioEngine::Cleanup() {
    if (!initialized_) {
        return;
    }

    std::cout << "Cleaning up Audio Engine..." << std::endl;
    
    StopAllNotes();
    CleanupMIDI();
    CleanupBASS();
    
    initialized_ = false;
    std::cout << "Audio Engine cleaned up" << std::endl;
}

bool AudioEngine::LoadSoundfont(const std::string& soundfont_path) {
    if (!initialized_ || !BASS_MIDI_FontInit_ptr || !BASS_MIDI_StreamSetFonts_ptr) {
        std::cerr << "Audio engine not initialized or BASS functions not available" << std::endl;
        return false;
    }

    // Free existing soundfont if any
    if (soundfont_ != 0 && BASS_MIDI_FontFree_ptr) {
        BASS_MIDI_FontFree_ptr(soundfont_);
        soundfont_ = 0;
        current_soundfont_path_.clear();
    }

    // Try different paths for soundfont
    std::string exe_dir = GetExecutableDirectory();
    std::vector<std::string> soundfont_paths;
    soundfont_paths.push_back(soundfont_path);                           // User specified path
    soundfont_paths.push_back(JoinPath(".", soundfont_path));             // Current directory
    soundfont_paths.push_back(JoinPath(exe_dir, soundfont_path));        // Executable directory
    soundfont_paths.push_back(JoinPath(JoinPath(".", "soundfonts"), soundfont_path)); // Soundfonts subdirectory
    soundfont_paths.push_back(JoinPath(JoinPath(exe_dir, "soundfonts"), soundfont_path)); // Executable/soundfonts directory
#ifndef _WIN32
    soundfont_paths.push_back(JoinPath("/usr/share/soundfonts", soundfont_path)); // System soundfonts directory
    soundfont_paths.push_back(JoinPath("/usr/local/share/soundfonts", soundfont_path)); // Local soundfonts directory
#endif

    HSOUNDFONT loaded_soundfont = 0;
    std::string successful_path;

    for (const auto& path : soundfont_paths) {
        loaded_soundfont = BASS_MIDI_FontInit_ptr(path.c_str(), BASS_MIDI_FONT_MMAP);
        if (loaded_soundfont != 0) {
            successful_path = path;
            std::cout << "BASS_MIDI_FontInit succeeded for: " << path << std::endl;
            break;
        } else {
            std::cout << "BASS_MIDI_FontInit failed for: " << path << " - " << GetLastBASSError() << std::endl;
        }
    }

    if (loaded_soundfont == 0) {
        std::cerr << "Failed to load soundfont from any location. Tried paths:" << std::endl;
        for (const auto& path : soundfont_paths) {
            std::cerr << "  " << path << std::endl;
        }
        std::cerr << "Note: You can download a soundfont file (.sf2) and place it in the executable directory" << std::endl;
        return false;
    }

    soundfont_ = loaded_soundfont;

    // Set the soundfont for the MIDI stream
    BASS_MIDI_FONT font;
    font.font = soundfont_;
    font.preset = -1;  // Use all presets
    font.bank = 0;     // Use bank 0

    if (!BASS_MIDI_StreamSetFonts_ptr(midi_stream_, &font, 1)) {
        std::cerr << "Failed to set soundfont for MIDI stream - " << GetLastBASSError() << std::endl;
        LogBASSError("BASS_MIDI_StreamSetFonts");
        return false;
    } else {
        std::cout << "BASS_MIDI_StreamSetFonts succeeded" << std::endl;
    }

    current_soundfont_path_ = successful_path;
    std::cout << "Soundfont loaded successfully from: " << successful_path << std::endl;
    return true;
}

void AudioEngine::PlayNote(int note, int velocity) {
    if (!initialized_ || midi_stream_ == 0 || !BASS_MIDI_StreamEvent_ptr) {
        std::cout << "Playing note: " << note << " (velocity: " << velocity << ") - Audio not available" << std::endl;
        return;
    }

    static bool channel_setup = false;
    if (!channel_setup) {
        std::cout << "Setting up MIDI channel 0..." << std::endl;
        if (!BASS_MIDI_StreamEvent_ptr(midi_stream_, 0, MIDI_EVENT_PROGRAM, 0)) {
            LogBASSError("BASS_MIDI_StreamEvent (Program Change)");
        }
        if (!BASS_MIDI_StreamEvent_ptr(midi_stream_, 0, MIDI_EVENT_VOLUME, 127)) {
            LogBASSError("BASS_MIDI_StreamEvent (Volume)");
        }
        if (!BASS_MIDI_StreamEvent_ptr(midi_stream_, 0, MIDI_EVENT_EXPRESSION, 127)) {
            LogBASSError("BASS_MIDI_StreamEvent (Expression)");
        }
        channel_setup = true;
    }

    unsigned int midi_param = MAKEWORD(note, velocity);
    if (!BASS_MIDI_StreamEvent_ptr(midi_stream_, 0, MIDI_EVENT_NOTE, midi_param)) {
        LogBASSError("BASS_MIDI_StreamEvent (PlayNote)");
    }
}

void AudioEngine::StopNote(int note) {
    if (!initialized_ || midi_stream_ == 0 || !BASS_MIDI_StreamEvent_ptr) {
        return;
    }
    unsigned int midi_param = MAKEWORD(note, 0);
    if (!BASS_MIDI_StreamEvent_ptr(midi_stream_, 0, MIDI_EVENT_NOTE, midi_param)) {
        LogBASSError("BASS_MIDI_StreamEvent (StopNote)");
    }
}

void AudioEngine::StopAllNotes() {
    if (!initialized_ || midi_stream_ == 0 || !BASS_MIDI_StreamEvent_ptr) {
        return;
    }
    for (int channel = 0; channel < 16; ++channel) {
        BASS_MIDI_StreamEvent_ptr(midi_stream_, channel, MIDI_EVENT_NOTESOFF, 0);
    }
}

void AudioEngine::SetVolume(float volume) {
    master_volume_ = std::max(0.0f, std::min(1.0f, volume));
    if (initialized_ && midi_stream_ != 0 && BASS_ChannelSetAttribute_ptr) {
        BASS_ChannelSetAttribute_ptr(midi_stream_, BASS_ATTRIB_VOL, master_volume_);
    }
}

float AudioEngine::GetVolume() const {
    return master_volume_;
}

bool AudioEngine::IsInitialized() const {
    return initialized_;
}

bool AudioEngine::IsSoundfontLoaded() const {
    return soundfont_ != 0;
}

const std::string& AudioEngine::GetCurrentSoundfontPath() const {
    return current_soundfont_path_;
}

#ifdef _WIN32
bool AudioEngine::LoadBASSLibraries() {
    std::string exe_dir = GetExecutableDirectory();

    std::vector<std::string> bass_paths_vec = { "bass.dll", JoinPath(exe_dir, "bass.dll") };
    for (const auto& path : bass_paths_vec) {
        bass_lib_ = LoadLibraryA(path.c_str());
        if (bass_lib_) {
            std::cout << "Loaded BASS library from: " << path << std::endl;
            break;
        }
    }
    if (!bass_lib_) {
        std::cerr << "Failed to load bass.dll" << std::endl;
        return false;
    }

    std::vector<std::string> bassmidi_paths_vec = { "bassmidi.dll", JoinPath(exe_dir, "bassmidi.dll") };
    for (const auto& path : bassmidi_paths_vec) {
        bassmidi_lib_ = LoadLibraryA(path.c_str());
        if (bassmidi_lib_) {
            std::cout << "Loaded BASSMIDI library from: " << path << std::endl;
            break;
        }
    }
    if (!bassmidi_lib_) {
        std::cerr << "Failed to load bassmidi.dll" << std::endl;
        FreeLibrary((HMODULE)bass_lib_);
        bass_lib_ = nullptr;
        return false;
    }

    std::vector<std::string> bass_fx_paths_vec = { "bass_fx.dll", JoinPath(exe_dir, "bass_fx.dll") };
    for (const auto& path : bass_fx_paths_vec) {
        bass_fx_lib_ = LoadLibraryA(path.c_str());
        if (bass_fx_lib_) {
            std::cout << "Loaded BASS_FX library from: " << path << std::endl;
            break;
        }
    }
    if (!bass_fx_lib_) {
        std::cout << "BASS_FX library not found, but it's optional" << std::endl;
    }

    #define LOAD_BASS_FUNC(lib, func) func##_ptr = (decltype(func##_ptr))GetProcAddress((HMODULE)lib, #func)
    
    LOAD_BASS_FUNC(bass_lib_, BASS_Init);
    LOAD_BASS_FUNC(bass_lib_, BASS_Free);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelPlay);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelSetAttribute);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelGetAttribute);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelIsActive);
    LOAD_BASS_FUNC(bass_lib_, BASS_StreamFree);
    LOAD_BASS_FUNC(bass_lib_, BASS_ErrorGetCode);
    LOAD_BASS_FUNC(bass_lib_, BASS_GetDeviceInfo);
    LOAD_BASS_FUNC(bass_lib_, BASS_SetConfig);

    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamCreate);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamEvent);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_FontInit);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_FontFree);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamSetFonts);

    #undef LOAD_BASS_FUNC

    if (!BASS_Init_ptr || !BASS_Free_ptr || !BASS_MIDI_StreamCreate_ptr ||
        !BASS_ChannelPlay_ptr || !BASS_MIDI_StreamEvent_ptr) {
        std::cerr << "Failed to load required BASS functions" << std::endl;
        UnloadBASSLibraries();
        return false;
    }

    std::cout << "BASS libraries loaded successfully" << std::endl;
    return true;
}

void AudioEngine::UnloadBASSLibraries() {
    if (bass_fx_lib_) FreeLibrary((HMODULE)bass_fx_lib_);
    if (bassmidi_lib_) FreeLibrary((HMODULE)bassmidi_lib_);
    if (bass_lib_) FreeLibrary((HMODULE)bass_lib_);
    
    bass_lib_ = nullptr;
    bassmidi_lib_ = nullptr;
    bass_fx_lib_ = nullptr;

    BASS_Init_ptr = nullptr;
    BASS_Free_ptr = nullptr;
    BASS_MIDI_StreamCreate_ptr = nullptr;
    BASS_ChannelPlay_ptr = nullptr;
    BASS_ChannelSetAttribute_ptr = nullptr;
    BASS_StreamFree_ptr = nullptr;
    BASS_MIDI_StreamEvent_ptr = nullptr;
    BASS_MIDI_FontInit_ptr = nullptr;
    BASS_MIDI_FontFree_ptr = nullptr;
    BASS_MIDI_StreamSetFonts_ptr = nullptr;
    BASS_ErrorGetCode_ptr = nullptr;
    BASS_ChannelGetAttribute_ptr = nullptr;
    BASS_ChannelIsActive_ptr = nullptr;
    BASS_GetDeviceInfo_ptr = nullptr;
    BASS_SetConfig_ptr = nullptr;
}

#else // NOT _WIN32

bool AudioEngine::LoadBASSLibraries() {
    std::string exe_dir = GetExecutableDirectory();

    const char* bass_paths[] = { "libbass.so", "./libbass.so", JoinPath(exe_dir, "libbass.so").c_str() };
    for (const char* path : bass_paths) {
        bass_lib_ = dlopen(path, RTLD_LAZY);
        if (bass_lib_) {
            std::cout << "Loaded BASS library from: " << path << std::endl;
            break;
        }
    }
    if (!bass_lib_) {
        std::cerr << "Failed to load libbass.so. Error: " << dlerror() << std::endl;
        return false;
    }

    const char* bassmidi_paths[] = { "libbassmidi.so", "./libbassmidi.so", JoinPath(exe_dir, "libbassmidi.so").c_str() };
    for (const char* path : bassmidi_paths) {
        bassmidi_lib_ = dlopen(path, RTLD_LAZY);
        if (bassmidi_lib_) {
            std::cout << "Loaded BASSMIDI library from: " << path << std::endl;
            break;
        }
    }
    if (!bassmidi_lib_) {
        std::cerr << "Failed to load libbassmidi.so. Error: " << dlerror() << std::endl;
        dlclose(bass_lib_);
        return false;
    }
    
    const char* bass_fx_paths[] = { "libbass_fx.so", "./libbass_fx.so", JoinPath(exe_dir, "libbass_fx.so").c_str() };
    for (const char* path : bass_fx_paths) {
        bass_fx_lib_ = dlopen(path, RTLD_LAZY);
        if (bass_fx_lib_) {
            std::cout << "Loaded BASS_FX library from: " << path << std::endl;
            break;
        }
    }
     if (!bass_fx_lib_) {
        std::cout << "BASS_FX library not found, but it's optional" << std::endl;
    }

    #define LOAD_BASS_FUNC(lib, func) func##_ptr = (decltype(func##_ptr))dlsym(lib, #func)

    LOAD_BASS_FUNC(bass_lib_, BASS_Init);
    LOAD_BASS_FUNC(bass_lib_, BASS_Free);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelPlay);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelSetAttribute);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelGetAttribute);
    LOAD_BASS_FUNC(bass_lib_, BASS_ChannelIsActive);
    LOAD_BASS_FUNC(bass_lib_, BASS_StreamFree);
    LOAD_BASS_FUNC(bass_lib_, BASS_ErrorGetCode);
    LOAD_BASS_FUNC(bass_lib_, BASS_GetDeviceInfo);
    LOAD_BASS_FUNC(bass_lib_, BASS_SetConfig);

    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamCreate);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamEvent);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_FontInit);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_FontFree);
    LOAD_BASS_FUNC(bassmidi_lib_, BASS_MIDI_StreamSetFonts);

    #undef LOAD_BASS_FUNC

    if (!BASS_Init_ptr || !BASS_Free_ptr || !BASS_MIDI_StreamCreate_ptr ||
        !BASS_ChannelPlay_ptr || !BASS_MIDI_StreamEvent_ptr) {
        std::cerr << "Failed to load required BASS functions" << std::endl;
        UnloadBASSLibraries();
        return false;
    }
    return true;
}

void AudioEngine::UnloadBASSLibraries() {
    if (bass_fx_lib_) dlclose(bass_fx_lib_);
    if (bassmidi_lib_) dlclose(bassmidi_lib_);
    if (bass_lib_) dlclose(bass_lib_);

    bass_lib_ = nullptr;
    bassmidi_lib_ = nullptr;
    bass_fx_lib_ = nullptr;

    BASS_Init_ptr = nullptr;
    BASS_Free_ptr = nullptr;
    BASS_MIDI_StreamCreate_ptr = nullptr;
    BASS_ChannelPlay_ptr = nullptr;
    BASS_ChannelSetAttribute_ptr = nullptr;
    BASS_StreamFree_ptr = nullptr;
    BASS_MIDI_StreamEvent_ptr = nullptr;
    BASS_MIDI_FontInit_ptr = nullptr;
    BASS_MIDI_FontFree_ptr = nullptr;
    BASS_MIDI_StreamSetFonts_ptr = nullptr;
    BASS_ErrorGetCode_ptr = nullptr;
    BASS_ChannelGetAttribute_ptr = nullptr;
    BASS_ChannelIsActive_ptr = nullptr;
    BASS_GetDeviceInfo_ptr = nullptr;
    BASS_SetConfig_ptr = nullptr;
}
#endif

std::string GetBASSErrorString(int errorCode) {
    switch (errorCode) {
        case 0: return "BASS_OK - All is OK";
        case 1: return "BASS_ERROR_MEM - Memory error";
        case 2: return "BASS_ERROR_FILEOPEN - Can't open the file";
        case 3: return "BASS_ERROR_DRIVER - Can't find a free/valid driver";
        case 4: return "BASS_ERROR_BUFLOST - The sample buffer was lost";
        case 5: return "BASS_ERROR_HANDLE - Invalid handle";
        case 6: return "BASS_ERROR_FORMAT - Unsupported sample format";
        case 7: return "BASS_ERROR_POSITION - Invalid position";
        case 8: return "BASS_ERROR_INIT - BASS_Init has not been successfully called";
        case 9: return "BASS_ERROR_START - BASS_Start has not been successfully called";
        case 14: return "BASS_ERROR_ALREADY - Already initialized/paused/whatever";
        case 18: return "BASS_ERROR_NOCHAN - Can't get a free channel";
        case 19: return "BASS_ERROR_ILLTYPE - An illegal type was specified";
        case 20: return "BASS_ERROR_ILLPARAM - An illegal parameter was specified";
        case 21: return "BASS_ERROR_NO3D - No 3D support";
        case 22: return "BASS_ERROR_NOEAX - No EAX support";
        case 23: return "BASS_ERROR_DEVICE - Illegal device number";
        case 24: return "BASS_ERROR_NOPLAY - Not playing";
        case 25: return "BASS_ERROR_FREQ - Illegal sample rate";
        case 27: return "BASS_ERROR_NOTFILE - The stream is not a file stream";
        case 29: return "BASS_ERROR_NOHW - No hardware voices available";
        case 31: return "BASS_ERROR_EMPTY - The MOD music has no sequence data";
        case 32: return "BASS_ERROR_NONET - No internet connection could be opened";
        case 33: return "BASS_ERROR_CREATE - Couldn't create the file";
        case 34: return "BASS_ERROR_NOFX - Effects are not available";
        case 37: return "BASS_ERROR_NOTAVAIL - Requested data is not available";
        case 38: return "BASS_ERROR_DECODE - The channel is/was decoding";
        case 39: return "BASS_ERROR_DX - A sufficient DirectX version is not installed";
        case 40: return "BASS_ERROR_TIMEOUT - Connection timed out";
        case 41: return "BASS_ERROR_FILEFORM - Unsupported file format";
        case 42: return "BASS_ERROR_SPEAKER - Unavailable speaker";
        case 43: return "BASS_ERROR_VERSION - Invalid BASS version";
        case 44: return "BASS_ERROR_CODEC - Codec is not available/supported";
        case 45: return "BASS_ERROR_ENDED - The channel/file has ended";
        case 46: return "BASS_ERROR_BUSY - The device is busy";
        default: return "Unknown error code: " + std::to_string(errorCode);
    }
}

bool AudioEngine::InitializeBASS() {
    if (!BASS_Init_ptr) {
        std::cerr << "BASS_Init function not available" << std::endl;
        return false;
    }

    // Set config for lower latency before init
    if (BASS_SetConfig_ptr) {
        BASS_SetConfig_ptr(BASS_CONFIG_UPDATEPERIOD, 10);
        BASS_SetConfig_ptr(BASS_CONFIG_BUFFER, 20);

        // Try to force ALSA usage on Linux
        #ifndef _WIN32
        BASS_SetConfig_ptr(BASS_CONFIG_DEV_DEFAULT, 1); // Try to use first available device as default
        #endif
    }

    // Enumerate available devices before attempting initialization
    std::cout << "Enumerating audio devices before initialization..." << std::endl;
    PrintDeviceInfo();

    // Try different initialization strategies for Linux audio systems
    bool initialized = false;

    // Strategy 1: Try device 1 first (often the first real ALSA device)
    std::cout << "Attempting to initialize BASS with device 1..." << std::endl;
    if (BASS_Init_ptr(1, 44100, 0, nullptr, nullptr)) {
        std::cout << "Successfully initialized with device 1" << std::endl;
        initialized = true;
    } else {
        LogBASSError("BASS_Init with device 1");

        // Strategy 2: Try default device
        std::cout << "Attempting to initialize BASS with BASS_DEVICE_DEFAULT..." << std::endl;
        if (BASS_Init_ptr(BASS_DEVICE_DEFAULT, 44100, 0, nullptr, nullptr)) {
            std::cout << "Successfully initialized with BASS_DEVICE_DEFAULT" << std::endl;
            initialized = true;
        } else {
            LogBASSError("BASS_Init with BASS_DEVICE_DEFAULT");

            // Strategy 3: Try device -1
            std::cout << "Trying device -1..." << std::endl;
            if (BASS_Init_ptr(-1, 44100, 0, nullptr, nullptr)) {
                std::cout << "Successfully initialized with device -1" << std::endl;
                initialized = true;
            } else {
                LogBASSError("BASS_Init with -1");

                // Strategy 4: Try multiple device numbers
                for (int device = 2; device <= 5; device++) {
                    std::cout << "Trying device " << device << "..." << std::endl;
                    if (BASS_Init_ptr(device, 44100, 0, nullptr, nullptr)) {
                        std::cout << "Successfully initialized with device " << device << std::endl;
                        initialized = true;
                        break;
                    } else {
                        LogBASSError("BASS_Init with device " + std::to_string(device));
                    }
                }

                // Final fallback: use device 0 (no sound) to allow the app to run
                if (!initialized) {
                    std::cout << "All real devices failed. Falling back to device 0 (no sound)..." << std::endl;
                    if (BASS_Init_ptr(0, 44100, 0, nullptr, nullptr)) {
                        std::cout << "BASS initialized with no sound device. Audio will be silent." << std::endl;
                        initialized = true;
                    } else {
                        LogBASSError("BASS_Init with device 0 (no sound)");
                        return false;
                    }
                }
            }
        }
    }
    std::cout << "BASS initialized successfully" << std::endl;
    PrintDeviceInfo();
    return true;
}

bool AudioEngine::InitializeMIDI() {
    if (!BASS_MIDI_StreamCreate_ptr || !BASS_ChannelPlay_ptr) {
        std::cerr << "BASSMIDI functions not available" << std::endl;
        return false;
    }
    midi_stream_ = BASS_MIDI_StreamCreate_ptr(16, BASS_SAMPLE_FLOAT, 0);
    if (midi_stream_ == 0) {
        LogBASSError("BASS_MIDI_StreamCreate");
        return false;
    }
    BASS_ChannelPlay_ptr(midi_stream_, FALSE);
    SetVolume(master_volume_);
    return true;
}

void AudioEngine::CleanupBASS() {
    if (BASS_Free_ptr) {
        BASS_Free_ptr();
    }
}

void AudioEngine::CleanupMIDI() {
    if (soundfont_ != 0 && BASS_MIDI_FontFree_ptr) {
        BASS_MIDI_FontFree_ptr(soundfont_);
        soundfont_ = 0;
    }
    if (midi_stream_ != 0 && BASS_StreamFree_ptr) {
        BASS_StreamFree_ptr(midi_stream_);
        midi_stream_ = 0;
    }
}

std::string AudioEngine::GetLastBASSError() const {
    if (!BASS_ErrorGetCode_ptr) {
        return "BASS_ErrorGetCode function not available";
    }
    return GetBASSErrorString(BASS_ErrorGetCode_ptr());
}

void AudioEngine::LogBASSError(const std::string& operation) const {
    std::cerr << "BASS Error in " << operation << ": " << GetLastBASSError() << std::endl;
}

int AudioEngine::GetCurrentPolyphony() const {
    if (!initialized_ || midi_stream_ == 0 || !BASS_ChannelGetAttribute_ptr) {
        return 0;
    }
    float voices = 0.0f;
    if (BASS_ChannelGetAttribute_ptr(midi_stream_, BASS_ATTRIB_MIDI_VOICES_ACTIVE, &voices)) {
        return static_cast<int>(voices);
    }
    return 0;
}

int AudioEngine::GetMaxPolyphony() const {
    if (!initialized_ || midi_stream_ == 0 || !BASS_ChannelGetAttribute_ptr) {
        return max_polyphony_;
    }
    float max_voices = 0.0f;
    if (BASS_ChannelGetAttribute_ptr(midi_stream_, BASS_ATTRIB_MIDI_VOICES, &max_voices)) {
        return static_cast<int>(max_voices);
    }
    return max_polyphony_;
}

void AudioEngine::PlayTestTone() {
    if (!initialized_) return;
    PlayNote(60, 100); // Middle C
}

void AudioEngine::PrintDeviceInfo() {
    if (!BASS_GetDeviceInfo_ptr) {
        std::cout << "BASS_GetDeviceInfo not available" << std::endl;
        return;
    }
    std::cout << "=== BASS Audio Devices === " << std::endl;
    BASS_DEVICEINFO info;
    int device_count = 0;

    // Check device 0 (no sound)
    if (BASS_GetDeviceInfo_ptr(0, &info)) {
        std::cout << "Device 0: " << info.name << " (no sound)" << std::endl;
        device_count++;
    }

    // Check real devices starting from 1
    for (int i = 1; BASS_GetDeviceInfo_ptr(i, &info); i++) {
        std::cout << "Device " << i << ": " << info.name;
        if (info.flags & BASS_DEVICE_DEFAULT) std::cout << " (default)";
        if (info.flags & BASS_DEVICE_ENABLED) std::cout << " (enabled)";
        if (info.flags & BASS_DEVICE_INIT) std::cout << " (initialized)";
        std::cout << std::endl;
        device_count++;
    }

    if (device_count == 0) {
        std::cout << "No audio devices found!" << std::endl;
    } else {
        std::cout << "Total devices found: " << device_count << std::endl;
    }
    std::cout << "==========================" << std::endl;
}