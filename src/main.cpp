#include <iostream>
#include <string>
#include <cstring>
#if defined(_WIN32)
#include <windows.h>
#endif
#include <GLFW/glfw3.h>
#include <GL/gl.h>
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>
#include "piano_keyboard.h"
#include "opengl_renderer.h"
#include "audio_engine.h"

// Global variables for mouse input
double g_mouse_x = 0.0;
double g_mouse_y = 0.0;
bool g_mouse_is_down = false;

// Global variables for window management
PianoKeyboard* g_piano = nullptr;
OpenGLRenderer* g_renderer = nullptr;
AudioEngine* g_audio_engine = nullptr;

// Global variables for audio
AudioEngine g_audio;
bool g_audio_initialized = false;

// Error callback for GLFW
void glfw_error_callback(int error, const char* description) {
    std::cerr << "GLFW Error " << error << ": " << description << std::endl;
}

// Mouse callback for GLFW
void mouse_button_callback(GLFWwindow* window, int button, int action, int mods) {
    if (button == GLFW_MOUSE_BUTTON_LEFT) {
        if (action == GLFW_PRESS) {
            g_mouse_is_down = true;
        } else if (action == GLFW_RELEASE) {
            g_mouse_is_down = false;
        }
    }
}

void cursor_position_callback(GLFWwindow* window, double xpos, double ypos) {
    g_mouse_x = xpos;
    g_mouse_y = ypos;
}

void window_size_callback(GLFWwindow* window, int width, int height) {
    if (g_piano && g_renderer) {
        g_renderer->SetViewport(width, height);
        g_piano->UpdateLayout(width, height);
    }
}

int main(int argc, char** argv) {
    std::cout << "Starting Piano Keyboard Application..." << std::endl;
    std::cout.flush();

    // Setup GLFW
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit()) {
        std::cerr << "Failed to initialize GLFW" << std::endl;
        return -1;
    }

    // GL 3.0 + GLSL 130
    const char* glsl_version = "#version 130";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 0);

    // Create window with graphics context
    GLFWwindow* window = glfwCreateWindow(1280, 720, "Piano Keyboard", NULL, NULL);
    if (window == NULL) {
        std::cerr << "Failed to create GLFW window" << std::endl;
        glfwTerminate();
        return -1;
    }
    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // Enable vsync

    // Set GLFW callbacks
    glfwSetMouseButtonCallback(window, mouse_button_callback);
    glfwSetCursorPosCallback(window, cursor_position_callback);
    glfwSetWindowSizeCallback(window, window_size_callback);

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup Dear ImGui style
    ImGui::StyleColorsDark();

    // Setup Platform/Renderer backends
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // Initialize OpenGL renderer
    OpenGLRenderer renderer;
    renderer.Initialize(1280, 720);

    // Initialize audio engine
    std::cout << "Initializing Audio Engine..." << std::endl;
    std::cout.flush();
    g_audio_initialized = g_audio.Initialize();
    if (!g_audio_initialized) {
        std::cerr << "Warning: Audio engine failed to initialize. Audio will be disabled." << std::endl;
        std::cerr.flush();
    } else {
        std::cout << "Audio engine initialized successfully!" << std::endl;
        std::cout.flush();
    }

    // Initialize piano keyboard
    PianoKeyboard piano;
    piano.Initialize(&g_audio);

    // Set global pointers for callbacks
    g_piano = &piano;
    g_renderer = &renderer;
    g_audio_engine = &g_audio;

    // Settings variables
    bool show_settings = true;
    bool show_debug = true;
    ImVec4 background_color = ImVec4(0.45f, 0.55f, 0.60f, 1.00f);
    Vec2 white_key_size = Vec2(20.0f, 120.0f);
    Vec2 black_key_size = Vec2(12.0f, 80.0f);
    bool auto_layout = true;
    float keyboard_margin = 50.0f;

    // Main loop
    while (!glfwWindowShouldClose(window)) {
        glfwPollEvents();

        // Start the Dear ImGui frame
        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();

        // Main menu bar
        if (ImGui::BeginMainMenuBar()) {
            if (ImGui::BeginMenu("View")) {
                ImGui::MenuItem("Settings", NULL, &show_settings);
                ImGui::MenuItem("Debug Info", NULL, &show_debug);
                ImGui::EndMenu();
            }
            if (ImGui::BeginMenu("Piano")) {
                if (ImGui::MenuItem("Reset All Keys")) {
                    for (int i = 0; i < 128; ++i) {
                        piano.SetKeyPressed(i, false);
                    }
                }
                ImGui::EndMenu();
            }
            ImGui::EndMainMenuBar();
        }

        // Update piano keyboard
        piano.Update();

        // Handle mouse input for piano keyboard
        piano.HandleInput(g_mouse_x, g_mouse_y, g_mouse_is_down);

        // Get window size for debug info
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);

        // Update renderer viewport
        renderer.SetViewport(display_w, display_h);

        // Update piano keyboard layout based on window size
        piano.UpdateLayout(display_w, display_h);

        // Settings window
        if (show_settings) {
            ImGui::Begin("Settings", &show_settings);

            ImGui::Text("Keyboard Settings");
            ImGui::Separator();

            if (ImGui::Checkbox("Auto Layout", &auto_layout)) {
                piano.SetAutoLayout(auto_layout);
            }

            if (auto_layout) {
                if (ImGui::SliderFloat("Keyboard Margin", &keyboard_margin, 10.0f, 200.0f)) {
                    piano.SetKeyboardMargin(keyboard_margin);
                }
            } else {
                if (ImGui::SliderFloat2("White Key Size", &white_key_size.x, 10.0f, 50.0f)) {
                    piano.SetWhiteKeySize(white_key_size);
                }

                if (ImGui::SliderFloat2("Black Key Size", &black_key_size.x, 5.0f, 30.0f)) {
                    piano.SetBlackKeySize(black_key_size);
                }
            }

            ImGui::Text("Audio Settings");
            ImGui::Separator();

            if (g_audio_initialized) {
                static bool audio_enabled = true;
                static float audio_volume = 0.8f;

                if (ImGui::Checkbox("Enable Audio", &audio_enabled)) {
                    piano.SetAudioEnabled(audio_enabled);
                }

                if (ImGui::SliderFloat("Volume", &audio_volume, 0.0f, 1.0f)) {
                    g_audio.SetVolume(audio_volume);
                }

                ImGui::Text("Soundfont Settings:");
                ImGui::Separator();

                // Show current soundfont status
                if (g_audio.IsSoundfontLoaded()) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Soundfont loaded:");
                    ImGui::Text("  %s", g_audio.GetCurrentSoundfontPath().c_str());
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "No soundfont loaded");
                    ImGui::Text("Using default MIDI sounds");
                }

                ImGui::Spacing();

                // Soundfont file input
                ImGui::Text("Load Soundfont File (.sf2):");
                static char soundfont_buffer[512] = "default.sf2";

                ImGui::PushItemWidth(-120);
                if (ImGui::InputText("##soundfont", soundfont_buffer, sizeof(soundfont_buffer))) {
                    // Update when user types
                }
                ImGui::PopItemWidth();

                ImGui::SameLine();
                if (ImGui::Button("Browse...")) {
                    // Simple file path suggestions
                    ImGui::OpenPopup("soundfont_suggestions");
                }

                // Popup with common soundfont locations
                if (ImGui::BeginPopup("soundfont_suggestions")) {
                    ImGui::Text("Common soundfont locations:");
                    ImGui::Separator();

                    if (ImGui::Selectable("./default.sf2")) {
                        strncpy(soundfont_buffer, "./default.sf2", sizeof(soundfont_buffer) - 1);
                    }
                    if (ImGui::Selectable("./soundfonts/piano.sf2")) {
                        strncpy(soundfont_buffer, "./soundfonts/piano.sf2", sizeof(soundfont_buffer) - 1);
                    }
                    if (ImGui::Selectable("./FluidR3_GM.sf2")) {
                        strncpy(soundfont_buffer, "./FluidR3_GM.sf2", sizeof(soundfont_buffer) - 1);
                    }
                    if (ImGui::Selectable("/usr/share/soundfonts/default.sf2")) {
                        strncpy(soundfont_buffer, "/usr/share/soundfonts/default.sf2", sizeof(soundfont_buffer) - 1);
                    }

                    ImGui::EndPopup();
                }

                if (ImGui::Button("Load Soundfont")) {
                    std::string soundfont_path(soundfont_buffer);
                    if (g_audio.LoadSoundfont(soundfont_path)) {
                        std::cout << "Soundfont loaded successfully" << std::endl;
                    } else {
                        std::cerr << "Failed to load soundfont: " << soundfont_path << std::endl;
                    }
                }

                ImGui::SameLine();
                if (ImGui::Button("Clear Soundfont")) {
                    // Clear the current soundfont to use default MIDI sounds
                    if (g_audio.LoadSoundfont("__clear_soundfont__")) {
                        std::cout << "Soundfont cleared, using default MIDI sounds" << std::endl;
                    }
                }

                ImGui::Spacing();
                ImGui::Separator();
                ImGui::Text("Audio Test:");

                if (ImGui::Button("Play Test Tone")) {
                    g_audio.PlayTestTone();
                }

                ImGui::SameLine();
                if (ImGui::Button("Show Devices")) {
                    g_audio.PrintDeviceInfo();
                }

                ImGui::Spacing();
                ImGui::TextWrapped("Note: Place .sf2 files in the executable directory or use full paths. Popular soundfonts include FluidR3_GM.sf2, GeneralUser_GS.sf2, or Timbres_Of_Heaven.sf2");
            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not available");
                ImGui::Text("BASS libraries may not be installed");
            }

            ImGui::Text("Display Settings");
            ImGui::Separator();

            ImGui::ColorEdit3("Background Color", (float*)&background_color);

            ImGui::Text("Windows");
            ImGui::Separator();

            ImGui::Checkbox("Show Debug Window", &show_debug);

            if (ImGui::Button("Reset All Keys")) {
                for (int i = 0; i < 128; ++i) {
                    piano.SetKeyPressed(i, false);
                }
            }

            ImGui::End();
        }

        // Piano keyboard is now rendered directly with OpenGL (see below)

        // Debug window
        if (show_debug) {
            ImGui::Begin("Debug Info", &show_debug);
            ImGui::Text("Application average %.3f ms/frame (%.1f FPS)",
                       1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);
            ImGui::Text("Pressed keys: %d", piano.GetPressedKeyCount());

            auto pressed_keys = piano.GetPressedKeys();
            if (!pressed_keys.empty()) {
                ImGui::Text("Active notes:");
                for (int note : pressed_keys) {
                    ImGui::SameLine();
                    ImGui::Text("%d", note);
                }
            }

            ImGui::Text("Mouse Position: (%.1f, %.1f)", ImGui::GetIO().MousePos.x, ImGui::GetIO().MousePos.y);
            ImGui::Text("Window Size: (%d, %d)", display_w, display_h);

            ImGui::Separator();
            ImGui::Text("Audio Engine: %s", g_audio.IsInitialized() ? "Initialized" : "Not Available");
            if (g_audio.IsInitialized()) {
                ImGui::Text("Audio Enabled: %s", piano.IsAudioEnabled() ? "Yes" : "No");
                ImGui::Text("Volume: %.2f", g_audio.GetVolume());
                ImGui::Text("Soundfont: %s", g_audio.IsSoundfontLoaded() ?
                    g_audio.GetCurrentSoundfontPath().c_str() : "None (using default sounds)");
                ImGui::Text("Polyphony: %d / %d", g_audio.GetCurrentPolyphony(), g_audio.GetMaxPolyphony());

                // Polyphony bar
                float polyphony_ratio = g_audio.GetMaxPolyphony() > 0 ?
                    (float)g_audio.GetCurrentPolyphony() / (float)g_audio.GetMaxPolyphony() : 0.0f;
                ImGui::ProgressBar(polyphony_ratio, ImVec2(-1, 0),
                    (std::to_string(g_audio.GetCurrentPolyphony()) + " / " + std::to_string(g_audio.GetMaxPolyphony())).c_str());
            }

            ImGui::End();
        }

        // Rendering
        ImGui::Render();

        // Clear screen with background color
        Color bg_color(background_color.x, background_color.y, background_color.z, background_color.w);
        renderer.Clear(bg_color);

        // Render piano keyboard with OpenGL
        piano.Render(renderer);

        // Render ImGui on top
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

        glfwSwapBuffers(window);
    }

    // Cleanup
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}
